{"version": 3, "sources": ["components/main-form/main-form.uvue"], "names": [], "mappings": "AA0BC,SAAQ;AACR,OAAO,SAAQ,MAAO,8BAA6B,CAAA;AACnD,OAAO,YAAW,MAAO,iCAAgC,CAAA;AACzD,OAAO,UAAS,MAAO,+BAA8B,CAAA;AACrD,OAAO,UAAS,MAAO,+BAA8B,CAAA;AACrD,OAAO,aAAY,MAAO,kCAAiC,CAAA;AAC3D,OAAO,SAAQ,MAAO,8BAA6B,CAAA;AACnD,OAAO,UAAS,MAAO,+BAA8B,CAAA;AACrD,OAAO,SAAQ,MAAO,8BAA6B,CAAA;AACnD,OAAO,aAAY,MAAO,kCAAiC,CAAA;AAC3D,OAAO,YAAW,MAAO,iCAAgC,CAAA;AACzD,OAAO,EAAE,aAAY,EAAG,eAAe,EAAC,QAAQ,EAAA,MAAO,sCAAqC,CAAA;AAK5F,MAAK,OAAQ,GAAE,eAAA,CAAA;IACd,IAAI,EAAE,WAAW;IACjB,UAAU,EAAE;QACX,SAAS;QACT,YAAY;QACZ,UAAU;QACV,UAAU;QACV,aAAa;QACb,SAAS;QACT,UAAU;QACV,SAAS;QACT,aAAa;QACb,YAAW;KACX;IACD,KAAK,EAAE;QACN,SAAQ;QACR,QAAQ,EAAE;YACT,IAAI,EAAE,KAAI,IAAK,QAAQ,CAAC,aAAa,EAAE,CAAC;YACxC,OAAO,EAAE,IAAK,KAAK,CAAC,aAAa,CAAA,CAAE,EAAC,CAAE,EAAC;SACvC;QACD,OAAM;QACN,KAAK,EAAE;YACN,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,EAAC;SACV;QAED,SAAQ;QACR,OAAO,EAAE;YACR,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,EAAC;SACX;KACA;IACD,QAAQ,EAAE,EAET;IACD,OAAO,EAAE;QACR,UAAU,CAAC,GAAE,EAAG,QAAQ,IAAE,CAAC;QAC3B,MAAM,CAAC,KAAK,EAAC,eAAe;YAE3B,WAAU;YACV,MAAM,UAAS,GAAI,KAAK,CAAC,KAAI,IAAK,MAAK,CAAA;YACvC,MAAM,UAAU,GAAC,KAAK,CAAC,KAAI,IAAK,GAAE,CAAA;YAClC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,KAAK,GAAC,UAAS,CAAA;QAE1C,CAAC;QAED;;;WAGE;QACF,gBAAgB,IAAI,GAAG,CAAC,MAAM,EAAE,GAAG,CAAA;YAClC,MAAM,SAAS,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAA,GAAI,IAAI,GAAG,EAAC,CAAA;YAE5C,sCAAqC;YACrC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,aAAa,EAAE,EAAC;gBAC5C,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAA,CAAA;YACnC,CAAC,CAAA,CAAA;YAED,OAAO,SAAQ,CAAA;QAChB,CAAA;KACD;CACD,CAAA,CAAA;;;;;;;;;;;;;;;WAtGA,GAAA,CAqBO,MAAA,EAAA,GAAA,CAAA,EArBD,KAAK,EAAC,WAAW,EAAA,CAAA,EAAA;QAEc,IAAA,CAAA,KAAK,IAAA,EAAA;cAAzC,GAAA,CAEO,MAAA,EAAA,GAAA,CAAA;;gBAFD,KAAK,EAAC,iBAAiB;;gBAC5B,GAAA,CAAyB,MAAA,EAAA,IAAA,EAAA,GAAA,CAAf,IAAA,CAAA,KAAK,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;;;QAIhB,GAAA,CAaO,MAAA,EAAA,GAAA,CAAA,EAbD,KAAK,EAAC,mBAAmB,EAAA,CAAA,EAAA;YAC9B,GAAA,CAWO,QAAA,EAAA,IAAA,EAAA,aAAA,CAAA,UAAA,CAX8C,IAAA,CAAA,QAAQ,EAAA,CAAxB,IAAI,EAAE,KAAK,EAAX,OAAI,EAAA,OAAA,GAAA,GAAA,CAAA,EAAA;uBAAzC,GAAA,CAWO,MAAA,EAAA,GAAA,CAAA,EAXD,KAAK,EAAC,gBAAgB,EAAA,CAAA,EAAA;oBACV,IAAI,CAAC,IAAI,IAAA,OAAA;0BAA1B,GAAA,CAAmI,oBAAA,EAAA,GAAA,CAAA;;4BAA7F,IAAI,EAAE,IAAI;4BAAG,KAAK,EAAE,KAAK;4BAAG,OAAO,EAAE,IAAA,CAAA,OAAO;4BAAG,QAAM,EAAE,IAAA,CAAA,MAAM;4BAAG,KAAG,EAAE,IAAA,CAAA,UAAU;;;oBACjG,IAAI,CAAC,IAAI,IAAA,UAAA;0BAA7B,GAAA,CAA4I,uBAAA,EAAA,GAAA,CAAA;;4BAAhG,IAAI,EAAE,IAAI;4BAAG,KAAK,EAAE,KAAK;4BAAG,OAAO,EAAE,IAAA,CAAA,OAAO;4BAAG,QAAM,EAAE,IAAA,CAAA,MAAM;4BAAG,KAAG,EAAE,IAAA,CAAA,UAAU;;;oBACzG,IAAI,CAAC,IAAI,IAAA,QAAA;0BAA3B,GAAA,CAAsI,qBAAA,EAAA,GAAA,CAAA;;4BAA9F,IAAI,EAAE,IAAI;4BAAG,KAAK,EAAE,KAAK;4BAAG,OAAO,EAAE,IAAA,CAAA,OAAO;4BAAG,QAAM,EAAE,IAAA,CAAA,MAAM;4BAAG,KAAG,EAAE,IAAA,CAAA,UAAU;;;oBACrG,IAAI,CAAC,IAAI,IAAA,QAAA;0BAA3B,GAAA,CAAsI,qBAAA,EAAA,GAAA,CAAA;;4BAA9F,IAAI,EAAE,IAAI;4BAAG,KAAK,EAAE,KAAK;4BAAG,OAAO,EAAE,IAAA,CAAA,OAAO;4BAAG,QAAM,EAAE,IAAA,CAAA,MAAM;4BAAG,KAAG,EAAE,IAAA,CAAA,UAAU;;;oBAClG,IAAI,CAAC,IAAI,IAAA,WAAA;0BAA9B,GAAA,CAA+I,wBAAA,EAAA,GAAA,CAAA;;4BAAjG,IAAI,EAAE,IAAI;4BAAG,KAAK,EAAE,KAAK;4BAAG,OAAO,EAAE,IAAA,CAAA,OAAO;4BAAG,QAAM,EAAE,IAAA,CAAA,MAAM;4BAAG,KAAG,EAAE,IAAA,CAAA,UAAU;;;oBAC5G,IAAI,CAAC,IAAI,IAAA,OAAA;0BAA1B,GAAA,CAAmI,oBAAA,EAAA,GAAA,CAAA;;4BAA7F,IAAI,EAAE,IAAI;4BAAG,KAAK,EAAE,KAAK;4BAAG,OAAO,EAAE,IAAA,CAAA,OAAO;4BAAG,QAAM,EAAE,IAAA,CAAA,MAAM;4BAAG,KAAG,EAAE,IAAA,CAAA,UAAU;;;oBACnG,IAAI,CAAC,IAAI,IAAA,QAAA;0BAA3B,GAAA,CAAsI,qBAAA,EAAA,GAAA,CAAA;;4BAA9F,IAAI,EAAE,IAAI;4BAAG,KAAK,EAAE,KAAK;4BAAG,OAAO,EAAE,IAAA,CAAA,OAAO;4BAAG,QAAM,EAAE,IAAA,CAAA,MAAM;4BAAG,KAAG,EAAE,IAAA,CAAA,UAAU;;;oBACtG,IAAI,CAAC,IAAI,IAAA,OAAA;0BAA1B,GAAA,CAAmI,oBAAA,EAAA,GAAA,CAAA;;4BAA7F,IAAI,EAAE,IAAI;4BAAG,KAAK,EAAE,KAAK;4BAAG,OAAO,EAAE,IAAA,CAAA,OAAO;4BAAG,QAAM,EAAE,IAAA,CAAA,MAAM;4BAAG,KAAG,EAAE,IAAA,CAAA,UAAU;;;oBAChG,IAAI,CAAC,IAAI,IAAA,WAAA;0BAA9B,GAAA,CAA+I,wBAAA,EAAA,GAAA,CAAA;;4BAAjG,IAAI,EAAE,IAAI;4BAAG,KAAK,EAAE,KAAK;4BAAG,OAAO,EAAE,IAAA,CAAA,OAAO;4BAAG,QAAM,EAAE,IAAA,CAAA,MAAM;4BAAG,KAAG,EAAE,IAAA,CAAA,UAAU;;;oBACzG,IAAI,CAAC,IAAI,IAAA,UAAA;0BAA7B,GAAA,CAA4I,uBAAA,EAAA,GAAA,CAAA;;4BAAhG,IAAI,EAAE,IAAI;4BAAG,KAAK,EAAE,KAAK;4BAAG,OAAO,EAAE,IAAA,CAAA,OAAO;4BAAG,QAAM,EAAE,IAAA,CAAA,MAAM;4BAAG,KAAG,EAAE,IAAA,CAAA,UAAU", "file": "components/main-form/main-form.uvue", "sourcesContent": ["<template>\r\n\t<view class=\"main-form\" >\r\n\t\t<!-- 表单标题 -->\r\n\t\t<view class=\"main-form-title\" v-if=\"title != ''\" >\r\n\t\t\t<text >{{ title }}</text>\r\n\t\t</view>\r\n\r\n\t\t<!-- 表单项容器 -->\r\n\t\t<view class=\"main-form-content\">\r\n\t\t\t<view class=\"main-form-item\" v-for=\"(item, index) in formData\">\r\n\t\t\t\t<FormInput v-if=\"item.type=='input'\" :data=\"item\" :index=\"index\" :keyName=\"keyName\" @change=\"change\" @msg=\"receiveMsg\"></FormInput>\r\n\t\t\t\t<FormTextarea v-if=\"item.type=='textarea'\" :data=\"item\" :index=\"index\" :keyName=\"keyName\" @change=\"change\" @msg=\"receiveMsg\"></FormTextarea>\r\n\t\t\t\t<FormSwitch v-if=\"item.type=='switch'\" :data=\"item\" :index=\"index\" :keyName=\"keyName\" @change=\"change\" @msg=\"receiveMsg\"></FormSwitch>\r\n\t\t\t\t<FormSlider v-if=\"item.type=='slider'\" :data=\"item\" :index=\"index\" :keyName=\"keyName\" @change=\"change\" @msg=\"receiveMsg\"></FormSlider>\r\n\t\t\t\t<FormNumberbox v-if=\"item.type=='numberbox'\" :data=\"item\" :index=\"index\" :keyName=\"keyName\" @change=\"change\" @msg=\"receiveMsg\"></FormNumberbox>\r\n\t\t\t\t<FormColor v-if=\"item.type=='color'\" :data=\"item\" :index=\"index\" :keyName=\"keyName\" @change=\"change\" @msg=\"receiveMsg\"></FormColor>\r\n\t\t\t\t<FormSelect v-if=\"item.type=='select'\" :data=\"item\" :index=\"index\" :keyName=\"keyName\" @change=\"change\" @msg=\"receiveMsg\"></FormSelect>\r\n\t\t\t\t<FormRadio v-if=\"item.type=='radio'\" :data=\"item\" :index=\"index\" :keyName=\"keyName\" @change=\"change\" @msg=\"receiveMsg\"></FormRadio>\r\n\t\t\t\t<FormYearmonth v-if=\"item.type=='yearmonth'\" :data=\"item\" :index=\"index\" :keyName=\"keyName\" @change=\"change\" @msg=\"receiveMsg\"></FormYearmonth>\r\n\t\t\t\t<FormDatetime v-if=\"item.type=='datetime'\" :data=\"item\" :index=\"index\" :keyName=\"keyName\" @change=\"change\" @msg=\"receiveMsg\"></FormDatetime>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script lang=\"uts\">\r\n\t// 导入表单组件\r\n\timport FormInput from './components/form-input.uvue'\r\n\timport FormTextarea from './components/form-textarea.uvue'\r\n\timport FormSwitch from './components/form-switch.uvue'\r\n\timport FormSlider from './components/form-slider.uvue'\r\n\timport FormNumberbox from './components/form-numberbox.uvue'\r\n\timport FormColor from './components/form-color.uvue'\r\n\timport FormSelect from './components/form-select.uvue'\r\n\timport FormRadio from './components/form-radio.uvue'\r\n\timport FormYearmonth from './components/form-yearmonth.uvue'\r\n\timport FormDatetime from './components/form-datetime.uvue'\r\n\timport { FormFieldData ,FormChangeEvent,MsgEvent} from '@/components/main-form/form_type.uts'\r\n\r\n\r\n\r\n\r\n\texport default {\r\n\t\tname: \"main-form\",\r\n\t\tcomponents: {\r\n\t\t\tFormInput,\r\n\t\t\tFormTextarea,\r\n\t\t\tFormSwitch,\r\n\t\t\tFormSlider,\r\n\t\t\tFormNumberbox,\r\n\t\t\tFormColor,\r\n\t\t\tFormSelect,\r\n\t\t\tFormRadio,\r\n\t\t\tFormYearmonth,\r\n\t\t\tFormDatetime\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\t// 表单数据数组\r\n\t\t\tformData: {\r\n\t\t\t\ttype: Array as PropType<FormFieldData[]>,\r\n\t\t\t\tdefault: () : Array<FormFieldData> => []\r\n\t\t\t},\r\n\t\t\t// 表单标题\r\n\t\t\ttitle: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"\"\r\n\t\t\t},\r\n\r\n\t\t\t// 存储键名前缀\r\n\t\t\tkeyName: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"\"\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\treceiveMsg(msg :MsgEvent){},\r\n\t\t\tchange(event:FormChangeEvent){\r\n\r\n\t\t\t\t// 获取当前字段索引\r\n\t\t\t\tconst fieldIndex = event.index as number\r\n\t\t\t\tconst fieldValue=event.value as any\r\n\t\t\t\tthis.formData[fieldIndex].value=fieldValue\r\n\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 将formData转换为Map对象\r\n\t\t\t * @returns Map<string, any> 以key为键，value为值的Map对象\r\n\t\t\t */\r\n\t\t\tgetFormDataAsMap(): Map<string, any> {\r\n\t\t\t\tconst resultMap: Map<string, any> = new Map()\r\n\r\n\t\t\t\t// 遍历formData数组，将每个字段的key和value添加到Map中\r\n\t\t\t\tthis.formData.forEach((item: FormFieldData) => {\r\n\t\t\t\t\tresultMap.set(item.key, item.value)\r\n\t\t\t\t})\r\n\r\n\t\t\t\treturn resultMap\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.main-form {\r\n\t\twidth: 100%;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t.main-form-title {\r\n\t\twidth: 710rpx;\r\n\t\tmargin: 0 auto;\r\n\t\tpadding: 0 20rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tborder-left: 10rpx solid #1F6ED4;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.main-form-content {\r\n\t\twidth: 100%;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t.main-form-item {\r\n\t\twidth: 100%;\r\n\t}\r\n</style>"]}