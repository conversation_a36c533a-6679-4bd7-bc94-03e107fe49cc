@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNIC178CB1
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import io.dcloud.uniapp.extapi.getStorage as uni_getStorage
import io.dcloud.uniapp.extapi.setStorage as uni_setStorage
open class GenComponentsMainFormComponentsFormRadio : VueComponent {
    constructor(__ins: ComponentInternalInstance) : super(__ins) {
        onCreated(fun(): Unit {
            val fieldObj = this.`$props`["data"] as FormFieldData
            this.initFieldData(fieldObj)
        }
        , __ins)
        this.`$watch`(fun(): Any? {
            return this.data
        }
        , fun(obj: FormFieldData) {
            val newValue = obj.value
            if (newValue !== this.fieldValue) {
                this.fieldValue = newValue
            }
        }
        , WatchOptions(deep = true))
    }
    @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
    override fun `$render`(): Any? {
        val _ctx = this
        val _cache = this.`$`.renderCache
        val _component_radio = resolveComponent("radio")
        val _component_radio_group = resolveComponent("radio-group")
        val _component_form_container = resolveComponent("form-container")
        return _cV(_component_form_container, _uM("label" to _ctx.fieldName, "show-error" to _ctx.showError, "tip" to _ctx.tip, "error-message" to _ctx.errorMessage, "label-color" to _ctx.labelColor, "background-color" to _ctx.backgroundColor), _uM("input-content" to withSlotCtx(fun(): UTSArray<Any> {
            return _uA(
                _cE("view", _uM("class" to "radio-container"), _uA(
                    _cV(_component_radio_group, _uM("onChange" to _ctx.onRadioChange), _uM("default" to withSlotCtx(fun(): UTSArray<Any> {
                        return _uA(
                            _cE(Fragment, null, RenderHelpers.renderList(_ctx.radioOptions, fun(option, index, __index, _cached): Any {
                                return _cE("view", _uM("class" to _nC(_uA(
                                    "radio-option",
                                    _uM("radio-option-last" to (index === _ctx.radioOptions.length - 1))
                                )), "key" to index), _uA(
                                    _cV(_component_radio, _uM("value" to option.value.toString(), "checked" to _ctx.isChecked(option.value), "color" to _ctx.radioColor, "class" to "radio-item"), null, 8, _uA(
                                        "value",
                                        "checked",
                                        "color"
                                    )),
                                    _cE("text", _uM("class" to "radio-text", "onClick" to fun(){
                                        _ctx.selectOption(option.value)
                                    }
                                    ), _tD(option.text), 9, _uA(
                                        "onClick"
                                    ))
                                ), 2)
                            }
                            ), 128)
                        )
                    }
                    ), "_" to 1), 8, _uA(
                        "onChange"
                    ))
                ))
            )
        }
        ), "_" to 1), 8, _uA(
            "label",
            "show-error",
            "tip",
            "error-message",
            "label-color",
            "background-color"
        ))
    }
    open var data: Any? by `$props`
    open var index: Number by `$props`
    open var keyName: String by `$props`
    open var labelColor: String by `$props`
    open var backgroundColor: String by `$props`
    open var fieldName: String by `$data`
    open var fieldValue: Any? by `$data`
    open var isSave: Boolean by `$data`
    open var save_key: String by `$data`
    open var tip: String by `$data`
    open var varType: String by `$data`
    open var radioOptions: UTSArray<RadioOption> by `$data`
    open var radioColor: String by `$data`
    open var showError: Boolean by `$data`
    open var errorMessage: String by `$data`
    @Suppress("USELESS_CAST")
    override fun data(): Map<String, Any?> {
        return _uM("fieldName" to "", "fieldValue" to null as Any?, "isSave" to false, "save_key" to "", "tip" to "", "varType" to "string", "radioOptions" to _uA<RadioOption>(), "radioColor" to "#007aff", "showError" to false, "errorMessage" to "")
    }
    open var initFieldData = ::gen_initFieldData_fn
    open fun gen_initFieldData_fn(fieldObj: FormFieldData): Unit {
        val fieldKey = fieldObj.key
        val fieldValue = fieldObj.value
        this.fieldName = fieldObj.name
        this.fieldValue = fieldValue
        this.isSave = fieldObj.isSave ?: false
        this.save_key = this.keyName + "_" + fieldKey
        val extalJson = fieldObj.extra as UTSJSONObject
        this.tip = extalJson.getString("tip") ?: ""
        val configVarType = extalJson.getString("varType") ?: "string"
        this.varType = this.validateVarType(configVarType)
        this.radioColor = extalJson.getString("radioColor") ?: "#007aff"
        val optionsArray = extalJson.getArray("options")
        if (optionsArray != null) {
            this.radioOptions = _uA()
            run {
                var i: Number = 0
                while(i < optionsArray.length){
                    val optionObj = optionsArray[i] as UTSJSONObject
                    val option = RadioOption(text = optionObj.getString("text") ?: "", value = optionObj.get("value") ?: "")
                    this.radioOptions.push(option)
                    i++
                }
            }
        }
        this.getCache()
    }
    open var validateVarType = ::gen_validateVarType_fn
    open fun gen_validateVarType_fn(varType: String): String {
        val validTypes = _uA(
            "string",
            "number"
        )
        if (validTypes.includes(varType)) {
            return varType
        } else {
            return "string"
        }
    }
    open fun isChecked(optionValue: Any): Boolean {
        if (this.fieldValue == null) {
            return false
        }
        return optionValue == this.fieldValue
    }
    open fun selectOption(optionValue: Any): Unit {
        var selectedValue: Any = optionValue
        if (this.varType == "number") {
            if (UTSAndroid.`typeof`(selectedValue) === "number") {
                selectedValue = selectedValue as Number
            } else {
                selectedValue = parseFloat((selectedValue as String).toString())
                if (isNaN(selectedValue as Number)) {
                    selectedValue = 0
                }
            }
        } else {
            selectedValue = selectedValue.toString()
        }
        val result = FormChangeEvent(index = this.index, value = selectedValue)
        this.change(result)
    }
    open var onRadioChange = ::gen_onRadioChange_fn
    open fun gen_onRadioChange_fn(event: UniRadioGroupChangeEvent): Unit {
        val selectedValue = event.detail.value
        val selectedOption = this.radioOptions.find(fun(option: RadioOption): Boolean {
            return option.value.toString() == selectedValue
        }
        )
        if (selectedOption != null) {
            this.selectOption(selectedOption.value)
        }
    }
    open var getCache = ::gen_getCache_fn
    open fun gen_getCache_fn(): Unit {
        if (this.isSave) {
            val that = this
            uni_getStorage(GetStorageOptions(key = this.save_key, success = fun(res: GetStorageSuccess){
                val cacheData = res.data as String
                var save_value: Any
                if (that.varType == "number") {
                    save_value = parseFloat(cacheData)
                    if (isNaN(save_value as Number)) {
                        return
                    }
                } else {
                    save_value = cacheData
                }
                that.fieldValue = save_value
                val result = FormChangeEvent(index = this.index, value = save_value)
                this.change(result)
            }
            ))
        }
    }
    open var setCache = ::gen_setCache_fn
    open fun gen_setCache_fn(): Unit {
        if (this.isSave && this.fieldValue != null) {
            val cacheValue = this.fieldValue!!.toString()
            uni_setStorage(SetStorageOptions(key = this.save_key, data = cacheValue))
        }
    }
    open var validate = ::gen_validate_fn
    open fun gen_validate_fn(): Boolean {
        if (this.fieldValue == null) {
            this.showError = true
            this.errorMessage = "请选择一个选项"
            return false
        }
        this.showError = false
        this.errorMessage = ""
        return true
    }
    open var change = ::gen_change_fn
    open fun gen_change_fn(event: FormChangeEvent): Unit {
        this.fieldValue = event.value
        this.setCache()
        this.`$emit`("change", event)
    }
    companion object {
        var name = "FormRadio"
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            _nCS(_uA(
                styles0
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return _uM("radio-container" to _pS(_uM("flex" to 1, "paddingTop" to "10rpx", "paddingRight" to "20rpx", "paddingBottom" to "10rpx", "paddingLeft" to "20rpx")), "radio-option" to _pS(_uM("display" to "flex", "flexDirection" to "row", "alignItems" to "center", "marginBottom" to "20rpx")), "radio-option-last" to _pS(_uM("marginBottom" to 0)), "radio-item" to _pS(_uM("marginRight" to "20rpx")), "radio-text" to _pS(_uM("flex" to 1, "fontSize" to "28rpx", "color" to "#333333", "lineHeight" to 1.4)))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = _uM()
        var emits: Map<String, Any?> = _uM("change" to null, "msg" to null)
        var props = _nP(_uM("data" to _uM(), "index" to _uM("type" to "Number", "default" to 0), "keyName" to _uM("type" to "String", "default" to ""), "labelColor" to _uM("type" to "String", "default" to "#000"), "backgroundColor" to _uM("type" to "String", "default" to "#f1f4f9")))
        var propsNeedCastKeys = _uA(
            "index",
            "keyName",
            "labelColor",
            "backgroundColor"
        )
        var components: Map<String, CreateVueComponent> = _uM("FormContainer" to GenComponentsMainFormComponentsFormContainerClass)
    }
}
