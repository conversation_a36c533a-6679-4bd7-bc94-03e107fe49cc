@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNIC178CB1
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
open class GenPagesIndexIndex : BasePage {
    constructor(__ins: ComponentInternalInstance, __renderer: String?) : super(__ins, __renderer) {}
    @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
    override fun `$render`(): Any? {
        val _ctx = this
        val _cache = this.`$`.renderCache
        val _component_main_color_picker = resolveEasyComponent("main-color-picker", GenComponentsMainColorPickerMainColorPickerClass)
        val _component_mainYearmonthPicker = resolveComponent("mainYearmonthPicker")
        val _component_main_datetime_picker = resolveComponent("main-datetime-picker")
        val _component_main_form = resolveEasyComponent("main-form", GenComponentsMainFormMainFormClass)
        return _cE("scroll-view", _uM("class" to "content"), _uA(
            _cE("button", _uM("class" to "test-button", "onClick" to _ctx.openColorPicker), "选择颜色", 8, _uA(
                "onClick"
            )),
            _cE("button", _uM("class" to "test-button", "onClick" to _ctx.openFun), "对话框", 8, _uA(
                "onClick"
            )),
            _cE("view", _uM("class" to "test-section"), _uA(
                _cE("text", _uM("class" to "section-title"), "日期时间选择器测试"),
                _cE("button", _uM("class" to "test-button", "onClick" to _ctx.testTimeRange), "时间范围 (time-range)", 8, _uA(
                    "onClick"
                )),
                _cE("button", _uM("class" to "test-button", "onClick" to _ctx.testMonth), "月份 (month)", 8, _uA(
                    "onClick"
                )),
                _cE("button", _uM("class" to "test-button", "onClick" to _ctx.testDay), "日期 (day)", 8, _uA(
                    "onClick"
                )),
                _cE("button", _uM("class" to "test-button", "onClick" to _ctx.testTime), "时间 (time)", 8, _uA(
                    "onClick"
                )),
                _cE("button", _uM("class" to "test-button", "onClick" to _ctx.testHourMinuteSecond), "时分秒 (hour-minute-second)", 8, _uA(
                    "onClick"
                )),
                _cE("button", _uM("class" to "test-button", "onClick" to _ctx.testYear), "年份 (year)", 8, _uA(
                    "onClick"
                )),
                _cE("button", _uM("class" to "test-button", "onClick" to _ctx.testYearMonth), "年月 (year-month)", 8, _uA(
                    "onClick"
                ))
            )),
            _cV(_component_main_color_picker, _uM("ref" to "colorPicker", "onCancel" to _ctx.onCancel, "onConfirm" to _ctx.onConfirm), null, 8, _uA(
                "onCancel",
                "onConfirm"
            )),
            _cV(_component_mainYearmonthPicker, _uM("ref" to "yearmonthPicker"), null, 512),
            _cV(_component_main_datetime_picker, _uM("ref" to "datetimePicker", "mode" to _ctx.dateTimeMode, "title" to _ctx.dateTimeTitle, "showSeconds" to _ctx.showSeconds, "quickOptions" to _ctx.quickOptions, "onCancel" to _ctx.onDateTimeCancel, "onConfirm" to _ctx.onDateTimeConfirm), null, 8, _uA(
                "mode",
                "title",
                "showSeconds",
                "quickOptions",
                "onCancel",
                "onConfirm"
            )),
            _cV(_component_main_form, _uM("formData" to _ctx.formConfig, "title" to "用户信息表单", "keyName" to "user_form", "ref" to "mainForm"), null, 8, _uA(
                "formData"
            )),
            _cE("button", _uM("class" to "test-button", "onClick" to _ctx.viewForm), "查看表单1", 8, _uA(
                "onClick"
            ))
        ))
    }
    open var dateTimeMode: String by `$data`
    open var dateTimeTitle: String by `$data`
    open var showSeconds: Boolean by `$data`
    open var quickOptions: UTSArray<DateQuickOption> by `$data`
    open var formConfig: UTSArray<FormFieldData> by `$data`
    @Suppress("USELESS_CAST")
    override fun data(): Map<String, Any?> {
        return _uM("dateTimeMode" to "time" as String, "dateTimeTitle" to "选择日期时间" as String, "showSeconds" to false as Boolean, "quickOptions" to _uA<DateQuickOption>(), "formConfig" to _uA<FormFieldData>(FormFieldData(key = "username", name = "用户名1", type = "input", value = "", isSave = true, extra = object : UTSJSONObject() {
            var minLength: Number = 0
            var maxLength: Number = 20
            var placeholder = "请输入用户名"
            var tip = "123"
            var inputmode = "digit"
        }), FormFieldData(key = "password", name = "密码", type = "input", value = "", isSave = false, extra = object : UTSJSONObject() {
            var minLength: Number = 6
            var maxLength: Number = 20
            var placeholder = "请输入密码"
            var tip = "密码请自己保管好"
            var inputmode = "number"
        }), FormFieldData(key = "email", name = "邮箱地址", type = "textarea", value = "", isSave = true, extra = object : UTSJSONObject() {
            var minLength: Number = 6
            var maxLength: Number = 20
            var placeholder = "请输入密码"
            var tip = ""
        }), FormFieldData(key = "enable_feature", name = "启用功能", type = "switch", value = 1, isSave = false, extra = object : UTSJSONObject() {
            var varType = "number"
            var tip = "开启后将启用此功能"
        }), FormFieldData(key = "slider", name = "slider测试", type = "slider", value = 10, isSave = true, extra = object : UTSJSONObject() {
            var min: Number = 0
            var max: Number = 100
            var step: Number = 1
        }), FormFieldData(key = "numberbox", name = "数量选择", type = "numberbox", value = 5, isSave = true, extra = object : UTSJSONObject() {
            var min: Number = 1
            var max: Number = 50
            var step: Number = 1
            var unit = "个"
            var tip = "请选择数量"
        }), FormFieldData(key = "themeColor", name = "主题颜色", type = "color", value = "", isSave = false, extra = object : UTSJSONObject() {
            var varType = "hex"
            var tip = "选择您喜欢的主题颜色"
        }), FormFieldData(key = "backgroundColor", name = "背景颜色", type = "color", value = "rgba(255, 0, 0, 0.8)", isSave = true, extra = object : UTSJSONObject() {
            var varType = "rgba"
            var tip = "选择背景颜色，支持透明度"
        }), FormFieldData(key = "birthYearMonth", name = "出生年月", type = "yearmonth", value = "", isSave = true, extra = object : UTSJSONObject() {
            var tip = "请选择您的出生年月1"
        }), FormFieldData(key = "city", name = "所在城市", type = "select", value = "", isSave = true, extra = object : UTSJSONObject() {
            var varType = "string"
            var placeholder = "请选择城市"
            var tip = "选择您所在的城市"
            var options = _uA(
                object : UTSJSONObject() {
                    var text = "北京"
                    var value = "beijing"
                },
                object : UTSJSONObject() {
                    var text = "上海"
                    var value = "shanghai"
                },
                object : UTSJSONObject() {
                    var text = "广州"
                    var value = "guangzhou"
                },
                object : UTSJSONObject() {
                    var text = "深圳"
                    var value = "shenzhen"
                },
                object : UTSJSONObject() {
                    var text = "杭州"
                    var value = "hangzhou"
                }
            )
        }), FormFieldData(key = "level", name = "用户等级", type = "select", value = 1, isSave = true, extra = object : UTSJSONObject() {
            var varType = "int"
            var placeholder = "请选择等级"
            var tip = "选择您的用户等级"
            var options = _uA(
                object : UTSJSONObject() {
                    var text = "初级用户"
                    var value: Number = 1
                },
                object : UTSJSONObject() {
                    var text = "中级用户"
                    var value: Number = 2
                },
                object : UTSJSONObject() {
                    var text = "高级用户"
                    var value: Number = 3
                },
                object : UTSJSONObject() {
                    var text = "VIP用户"
                    var value: Number = 4
                }
            )
        }), FormFieldData(key = "score", name = "评分", type = "select", value = 4.5, isSave = true, extra = object : UTSJSONObject() {
            var varType = "float"
            var placeholder = "请选择评分"
            var tip = "选择您的评分"
            var options = _uA(
                object : UTSJSONObject() {
                    var text = "1.0分"
                    var value: Number = 1.0
                },
                object : UTSJSONObject() {
                    var text = "2.5分"
                    var value: Number = 2.5
                },
                object : UTSJSONObject() {
                    var text = "3.0分"
                    var value: Number = 3.0
                },
                object : UTSJSONObject() {
                    var text = "4.5分"
                    var value: Number = 4.5
                },
                object : UTSJSONObject() {
                    var text = "5.0分"
                    var value: Number = 5.0
                }
            )
        }), FormFieldData(key = "appointmentTime", name = "预约时间", type = "datetime", value = "", isSave = true, extra = object : UTSJSONObject() {
            var varType = "datetime"
            var tip = "请选择预约的日期和时间"
        }), FormFieldData(key = "birthDate", name = "出生日期", type = "datetime", value = "", isSave = true, extra = object : UTSJSONObject() {
            var varType = "date"
            var tip = "请选择您的出生日期"
        }), FormFieldData(key = "workTime", name = "工作时间", type = "datetime", value = "", isSave = false, extra = object : UTSJSONObject() {
            var varType = "date-range"
            var tip = "请选择您的工作时间"
        }), FormFieldData(key = "graduationYear", name = "毕业年份", type = "datetime", value = "", isSave = true, extra = object : UTSJSONObject() {
            var varType = "year"
            var tip = "请选择您的毕业年份"
        }), FormFieldData(key = "gender", name = "性别选择", type = "radio", value = "male", isSave = true, extra = object : UTSJSONObject() {
            var varType = "string"
            var radioColor = "#007aff"
            var tip = "请选择您的性别"
            var options = _uA(
                object : UTSJSONObject() {
                    var text = "男"
                    var value = "male"
                },
                object : UTSJSONObject() {
                    var text = "女"
                    var value = "female"
                },
                object : UTSJSONObject() {
                    var text = "其他"
                    var value = "other"
                }
            )
        }), FormFieldData(key = "userLevel", name = "用户等级", type = "radio", value = 1, isSave = true, extra = object : UTSJSONObject() {
            var varType = "number"
            var radioColor = "#ff6b35"
            var tip = "请选择您的用户等级"
            var layoutDirection = "vertical"
            var options = _uA(
                object : UTSJSONObject() {
                    var text = "初级用户"
                    var value: Number = 1
                },
                object : UTSJSONObject() {
                    var text = "中级用户"
                    var value: Number = 2
                },
                object : UTSJSONObject() {
                    var text = "高级用户"
                    var value: Number = 3
                },
                object : UTSJSONObject() {
                    var text = "VIP用户"
                    var value: Number = 4
                }
            )
        })))
    }
    open var viewForm = ::gen_viewForm_fn
    open fun gen_viewForm_fn() {
        val formMap = (this.`$refs`["mainForm"] as ComponentPublicInstance).`$callMethod`("getFormDataAsMap")
        console.log(formMap, " at pages/index/index.uvue:312")
    }
    open var openFun = ::gen_openFun_fn
    open fun gen_openFun_fn() {
        (this.`$refs`["yearmonthPicker"] as ComponentPublicInstance).`$callMethod`("open")
    }
    open var openColorPicker = ::gen_openColorPicker_fn
    open fun gen_openColorPicker_fn() {
        (this.`$refs`["colorPicker"] as ComponentPublicInstance).`$callMethod`("open")
    }
    open var onCancel = ::gen_onCancel_fn
    open fun gen_onCancel_fn() {
        console.log("用户取消选择", " at pages/index/index.uvue:339")
    }
    open var onConfirm = ::gen_onConfirm_fn
    open fun gen_onConfirm_fn(result: UTSJSONObject) {
        console.log(result, " at pages/index/index.uvue:343")
        console.log("选择的颜色:", result["color"], " at pages/index/index.uvue:344")
        console.log("RGBA值:", result["rgba"], " at pages/index/index.uvue:345")
    }
    open var openDateTimePicker = ::gen_openDateTimePicker_fn
    open fun gen_openDateTimePicker_fn() {
        (this.`$refs`["datetimePicker"] as ComponentPublicInstance).`$callMethod`("show")
    }
    open var onDateTimeCancel = ::gen_onDateTimeCancel_fn
    open fun gen_onDateTimeCancel_fn() {
        console.log("用户取消选择日期时间", " at pages/index/index.uvue:355")
    }
    open var onDateTimeConfirm = ::gen_onDateTimeConfirm_fn
    open fun gen_onDateTimeConfirm_fn(result: UTSJSONObject) {
        console.log("选择的日期时间:", result, " at pages/index/index.uvue:360")
        console.log("格式化后的值:", result["formatted"], " at pages/index/index.uvue:361")
        console.log("Map值:", result["value"], " at pages/index/index.uvue:362")
        val value = result["value"]
        if (value is Map<*, *>) {
            console.log("Map详细信息:", " at pages/index/index.uvue:367")
            val map = value as Map<String, Any>
            if (map.has("year")) {
                console.log("年:", map.get("year"), " at pages/index/index.uvue:369")
            }
            if (map.has("month")) {
                console.log("月:", map.get("month"), " at pages/index/index.uvue:370")
            }
            if (map.has("day")) {
                console.log("日:", map.get("day"), " at pages/index/index.uvue:371")
            }
            if (map.has("hour")) {
                console.log("时:", map.get("hour"), " at pages/index/index.uvue:372")
            }
            if (map.has("minute")) {
                console.log("分:", map.get("minute"), " at pages/index/index.uvue:373")
            }
            if (map.has("second")) {
                console.log("秒:", map.get("second"), " at pages/index/index.uvue:374")
            }
        }
    }
    open var testTimeRange = ::gen_testTimeRange_fn
    open fun gen_testTimeRange_fn() {
        this.dateTimeMode = "time-range"
        this.dateTimeTitle = "选择时间范围"
        this.showSeconds = false
        this.openDateTimePicker()
    }
    open var testMonth = ::gen_testMonth_fn
    open fun gen_testMonth_fn() {
        this.dateTimeMode = "month"
        this.dateTimeTitle = "选择月份"
        this.showSeconds = false
        this.openDateTimePicker()
    }
    open var testDay = ::gen_testDay_fn
    open fun gen_testDay_fn() {
        this.dateTimeMode = "day"
        this.dateTimeTitle = "选择日期"
        this.showSeconds = false
        this.openDateTimePicker()
    }
    open var testTime = ::gen_testTime_fn
    open fun gen_testTime_fn() {
        this.dateTimeMode = "time"
        this.dateTimeTitle = "选择时间"
        this.showSeconds = false
        this.openDateTimePicker()
    }
    open var testHourMinuteSecond = ::gen_testHourMinuteSecond_fn
    open fun gen_testHourMinuteSecond_fn() {
        this.dateTimeMode = "hour-minute-second"
        this.dateTimeTitle = "选择时分秒"
        this.showSeconds = true
        this.openDateTimePicker()
    }
    open var testYear = ::gen_testYear_fn
    open fun gen_testYear_fn() {
        this.dateTimeMode = "year"
        this.dateTimeTitle = "选择年份"
        this.showSeconds = false
        this.openDateTimePicker()
    }
    open var testYearMonth = ::gen_testYearMonth_fn
    open fun gen_testYearMonth_fn() {
        this.dateTimeMode = "year-month"
        this.dateTimeTitle = "选择年月"
        this.showSeconds = false
        this.openDateTimePicker()
    }
    companion object {
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            _nCS(_uA(
                styles0
            ), _uA(
                GenApp.styles
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return _uM("content" to _pS(_uM("height" to "100%", "paddingTop" to "20rpx", "paddingRight" to "20rpx", "paddingBottom" to "20rpx", "paddingLeft" to "20rpx")), "test-section" to _pS(_uM("marginTop" to "40rpx", "marginRight" to 0, "marginBottom" to "40rpx", "marginLeft" to 0, "paddingTop" to "30rpx", "paddingRight" to "30rpx", "paddingBottom" to "30rpx", "paddingLeft" to "30rpx", "backgroundColor" to "#f8f9fa", "borderTopLeftRadius" to "16rpx", "borderTopRightRadius" to "16rpx", "borderBottomRightRadius" to "16rpx", "borderBottomLeftRadius" to "16rpx")), "section-title" to _pS(_uM("fontSize" to "32rpx", "fontWeight" to "bold", "color" to "#333333", "marginBottom" to "20rpx")), "test-button" to _pS(_uM("marginTop" to "10rpx", "marginRight" to 0, "marginBottom" to "10rpx", "marginLeft" to 0, "width" to "100%", "backgroundColor" to "#007AFF", "color" to "#FFFFFF", "borderTopWidth" to "medium", "borderRightWidth" to "medium", "borderBottomWidth" to "medium", "borderLeftWidth" to "medium", "borderTopStyle" to "none", "borderRightStyle" to "none", "borderBottomStyle" to "none", "borderLeftStyle" to "none", "borderTopColor" to "#000000", "borderRightColor" to "#000000", "borderBottomColor" to "#000000", "borderLeftColor" to "#000000", "borderTopLeftRadius" to "8rpx", "borderTopRightRadius" to "8rpx", "borderBottomRightRadius" to "8rpx", "borderBottomLeftRadius" to "8rpx", "paddingTop" to "20rpx", "paddingRight" to "20rpx", "paddingBottom" to "20rpx", "paddingLeft" to "20rpx", "fontSize" to "28rpx")))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = _uM()
        var emits: Map<String, Any?> = _uM()
        var props = _nP(_uM())
        var propsNeedCastKeys: UTSArray<String> = _uA()
        var components: Map<String, CreateVueComponent> = _uM("mainYearmonthPicker" to GenComponentsMainFormToolsMainYearmonthPickerClass, "mainDatetimePicker" to GenComponentsMainFormToolsMainDatetimePickerClass)
    }
}
