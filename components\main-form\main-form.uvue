<template>
	<view class="main-form" >
		<!-- 表单标题 -->
		<view class="main-form-title" v-if="title != ''" >
			<text >{{ title }}</text>
		</view>

		<!-- 表单项容器 -->
		<view class="main-form-content">
			<view class="main-form-item" v-for="(item, index) in formData">
				<FormInput v-if="item.type=='input'" :data="item" :index="index" :keyName="keyName" @change="change" @msg="receiveMsg"></FormInput>
				<FormTextarea v-if="item.type=='textarea'" :data="item" :index="index" :keyName="keyName" @change="change" @msg="receiveMsg"></FormTextarea>
				<FormSwitch v-if="item.type=='switch'" :data="item" :index="index" :keyName="keyName" @change="change" @msg="receiveMsg"></FormSwitch>
				<FormSlider v-if="item.type=='slider'" :data="item" :index="index" :keyName="keyName" @change="change" @msg="receiveMsg"></FormSlider>
				<FormNumberbox v-if="item.type=='numberbox'" :data="item" :index="index" :keyName="keyName" @change="change" @msg="receiveMsg"></FormNumberbox>
				<FormColor v-if="item.type=='color'" :data="item" :index="index" :keyName="keyName" @change="change" @msg="receiveMsg"></FormColor>
				<FormSelect v-if="item.type=='select'" :data="item" :index="index" :keyName="keyName" @change="change" @msg="receiveMsg"></FormSelect>
				<FormRadio v-if="item.type=='radio'" :data="item" :index="index" :keyName="keyName" @change="change" @msg="receiveMsg"></FormRadio>
				<FormYearmonth v-if="item.type=='yearmonth'" :data="item" :index="index" :keyName="keyName" @change="change" @msg="receiveMsg"></FormYearmonth>
				<FormDatetime v-if="item.type=='datetime'" :data="item" :index="index" :keyName="keyName" @change="change" @msg="receiveMsg"></FormDatetime>
			</view>
		</view>
	</view>
</template>

<script lang="uts">
	// 导入表单组件
	import FormInput from './components/form-input.uvue'
	import FormTextarea from './components/form-textarea.uvue'
	import FormSwitch from './components/form-switch.uvue'
	import FormSlider from './components/form-slider.uvue'
	import FormNumberbox from './components/form-numberbox.uvue'
	import FormColor from './components/form-color.uvue'
	import FormSelect from './components/form-select.uvue'
	import FormRadio from './components/form-radio.uvue'
	import FormYearmonth from './components/form-yearmonth.uvue'
	import FormDatetime from './components/form-datetime.uvue'
	import { FormFieldData ,FormChangeEvent,MsgEvent} from '@/components/main-form/form_type.uts'




	export default {
		name: "main-form",
		components: {
			FormInput,
			FormTextarea,
			FormSwitch,
			FormSlider,
			FormNumberbox,
			FormColor,
			FormSelect,
			FormRadio,
			FormYearmonth,
			FormDatetime
		},
		props: {
			// 表单数据数组
			formData: {
				type: Array as PropType<FormFieldData[]>,
				default: () : Array<FormFieldData> => []
			},
			// 表单标题
			title: {
				type: String,
				default: ""
			},

			// 存储键名前缀
			keyName: {
				type: String,
				default: ""
			}
		},
		computed: {
		
		},
		methods: {
			receiveMsg(msg :MsgEvent){},
			change(event:FormChangeEvent){

				// 获取当前字段索引
				const fieldIndex = event.index as number
				const fieldValue=event.value as any
				this.formData[fieldIndex].value=fieldValue

			},

			/**
			 * 将formData转换为Map对象
			 * @returns Map<string, any> 以key为键，value为值的Map对象
			 */
			getFormDataAsMap(): Map<string, any> {
				const resultMap: Map<string, any> = new Map()

				// 遍历formData数组，将每个字段的key和value添加到Map中
				this.formData.forEach((item: FormFieldData) => {
					resultMap.set(item.key, item.value)
				})

				return resultMap
			}
		}
	}
</script>

<style>
	.main-form {
		width: 100%;
		display: flex;
		flex-direction: column;
	}

	.main-form-title {
		width: 710rpx;
		margin: 0 auto;
		padding: 0 20rpx;
		margin-bottom: 20rpx;
		border-left: 10rpx solid #1F6ED4;
		display: flex;
		align-items: center;
	}

	.main-form-content {
		width: 100%;
		display: flex;
		flex-direction: column;
	}

	.main-form-item {
		width: 100%;
	}
</style>