{"version": 3, "sources": ["components/main-form/components/form-radio.uvue", "components/main-form/components/form-input.uvue"], "sourcesContent": ["<template>\n\t<form-container :label=\"fieldName\" :show-error=\"showError\" :tip=\"tip\" :error-message=\"errorMessage\" :label-color=\"labelColor\"\n\t\t:background-color=\"backgroundColor\">\n\t\t<template #input-content>\n\t\t\t<view :class=\"['radio-container', layoutDirection === 'horizontal' ? 'radio-container-horizontal' : 'radio-container-vertical']\">\n\t\t\t\t<radio-group @change=\"onRadioChange\">\n\t\t\t\t\t<view :class=\"['radio-option', { 'radio-option-last': index === radioOptions.length - 1 }]\"\n\t\t\t\t\t\tv-for=\"(option, index) in radioOptions\" :key=\"index\">\n\t\t\t\t\t\t<radio :value=\"option.value.toString()\" :checked=\"isChecked(option.value)\"\n\t\t\t\t\t\t\t:color=\"radioColor\" class=\"radio-item\"></radio>\n\t\t\t\t\t\t<text class=\"radio-text\" @click=\"selectOption(option.value)\">{{ option.text }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</radio-group>\n\t\t\t</view>\n\t\t</template>\n\t</form-container>\n</template>\n\n<script lang=\"uts\">\n\timport { FormFieldData, FormChangeEvent } from '@/components/main-form/form_type.uts'\n\timport FormContainer from './form-container.uvue'\n\n\t// 定义选项类型\n\ttype RadioOption = {\n\t\ttext: string;\n\t\tvalue: string | number;\n\t}\n\n\texport default {\n\t\tname: \"FormRadio\",\n\t\temits: ['change', 'msg'],\n\t\tcomponents: {\n\t\t\tFormContainer\n\t\t},\n\t\tprops: {\n\t\t\tdata: {\n\t\t\t\ttype: null as any as PropType<FormFieldData>\n\t\t\t},\n\t\t\tindex: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 0\n\t\t\t},\n\t\t\tkeyName: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"\"\n\t\t\t},\n\t\t\tlabelColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#000\"\n\t\t\t},\n\t\t\tbackgroundColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#f1f4f9\"\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tfieldName: \"\",\n\t\t\t\tfieldValue: null as string | number | null,\n\t\t\t\tisSave: false,\n\t\t\t\tsave_key: \"\",\n\t\t\t\ttip: \"\",\n\t\t\t\tvarType: \"string\",\n\t\t\t\tradioOptions: [] as RadioOption[],\n\t\t\t\tradioColor: \"#007aff\",\n\t\t\t\tlayoutDirection: \"vertical\",\n\t\t\t\tshowError: false,\n\t\t\t\terrorMessage: \"\"\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\n\t\t},\n\t\twatch: {\n\t\t\tdata: {\n\t\t\t\thandler(obj: FormFieldData) {\n\t\t\t\t\t// 只处理value的变化，当外部传入的value与当前fieldValue不同时，才更新fieldValue\n\t\t\t\t\tconst newValue = obj.value\n\t\t\t\t\tif (newValue !== this.fieldValue) {\n\t\t\t\t\t\tthis.fieldValue = newValue\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t}\n\t\t},\n\t\tcreated(): void {\n\t\t\t// 初始化时调用一次即可\n\t\t\tconst fieldObj = this.$props[\"data\"] as FormFieldData\n\t\t\tthis.initFieldData(fieldObj)\n\t\t},\n\t\tmethods: {\n\t\t\t// 初始化字段数据（仅在首次加载时调用）\n\t\t\tinitFieldData(fieldObj: FormFieldData): void {\n\t\t\t\tconst fieldKey = fieldObj.key\n\t\t\t\tconst fieldValue = fieldObj.value\n\n\t\t\t\t// 设置基本信息\n\t\t\t\tthis.fieldName = fieldObj.name\n\t\t\t\tthis.fieldValue = fieldValue\n\t\t\t\tthis.isSave = fieldObj.isSave ?? false\n\t\t\t\tthis.save_key = this.keyName + \"_\" + fieldKey\n\n\t\t\t\t// 解析配置信息\n\t\t\t\tconst extalJson = fieldObj.extra as UTSJSONObject\n\t\t\t\tthis.tip = extalJson.getString(\"tip\") ?? \"\"\n\t\t\t\tconst configVarType = extalJson.getString(\"varType\") ?? \"string\"\n\n\t\t\t\t// 校验 varType 的有效性\n\t\t\t\tthis.varType = this.validateVarType(configVarType)\n\t\t\t\tthis.radioColor = extalJson.getString(\"radioColor\") ?? \"#007aff\"\n\n\t\t\t\t// 解析布局方向，默认为竖向\n\t\t\t\tconst configLayoutDirection = extalJson.getString(\"layoutDirection\") ?? \"vertical\"\n\t\t\t\tthis.layoutDirection = this.validateLayoutDirection(configLayoutDirection)\n\n\t\t\t\t// 解析选项数据\n\t\t\t\tconst optionsArray = extalJson.getArray(\"options\")\n\t\t\t\tif (optionsArray != null) {\n\t\t\t\t\tthis.radioOptions = []\n\t\t\t\t\tfor (let i = 0; i < optionsArray.length; i++) {\n\t\t\t\t\t\tconst optionObj = optionsArray[i] as UTSJSONObject\n\t\t\t\t\t\tconst option: RadioOption = {\n\t\t\t\t\t\t\ttext: optionObj.getString(\"text\") ?? \"\",\n\t\t\t\t\t\t\tvalue: optionObj.get(\"value\") ?? \"\"\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.radioOptions.push(option)\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// 获取缓存\n\t\t\t\tthis.getCache()\n\t\t\t},\n\n\t\t\t// 校验 varType 的有效性\n\t\t\tvalidateVarType(varType: string): string {\n\t\t\t\tconst validTypes = [\"string\", \"number\"]\n\t\t\t\tif (validTypes.includes(varType)) {\n\t\t\t\t\treturn varType\n\t\t\t\t} else {\n\t\t\t\t\treturn \"string\"\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 校验 layoutDirection 的有效性\n\t\t\tvalidateLayoutDirection(layoutDirection: string): string {\n\t\t\t\tconst validDirections = [\"vertical\", \"horizontal\"]\n\t\t\t\tif (validDirections.includes(layoutDirection)) {\n\t\t\t\t\treturn layoutDirection\n\t\t\t\t} else {\n\t\t\t\t\treturn \"vertical\"\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 判断选项是否被选中\n\t\t\tisChecked(optionValue: string | number): boolean {\n\t\t\t\tif (this.fieldValue == null) {\n\t\t\t\t\treturn false\n\t\t\t\t}\n\t\t\t\t// 使用宽松比较来处理类型差异\n\t\t\t\treturn optionValue == this.fieldValue\n\t\t\t},\n\n\t\t\t// 选择选项\n\t\t\tselectOption(optionValue: string | number): void {\n\t\t\t\tlet selectedValue: string | number = optionValue\n\n\t\t\t\t// 根据varType转换类型\n\t\t\t\tif (this.varType == \"number\") {\n\t\t\t\t\tif (typeof selectedValue === \"number\") {\n\t\t\t\t\t\tselectedValue = selectedValue\n\t\t\t\t\t} else {\n\t\t\t\t\t\tselectedValue = parseFloat(selectedValue.toString())\n\t\t\t\t\t\tif (isNaN(selectedValue as number)) {\n\t\t\t\t\t\t\tselectedValue = 0\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\t// varType == \"string\"\n\t\t\t\t\tselectedValue = selectedValue.toString()\n\t\t\t\t}\n\n\t\t\t\tconst result: FormChangeEvent = {\n\t\t\t\t\tindex: this.index,\n\t\t\t\t\tvalue: selectedValue\n\t\t\t\t}\n\t\t\t\tthis.change(result)\n\t\t\t},\n\n\t\t\t// radio-group change事件处理\n\t\t\tonRadioChange(event: UniRadioGroupChangeEvent): void {\n\t\t\t\tconst selectedValue = event.detail.value\n\t\t\t\t// 查找对应的选项\n\t\t\t\tconst selectedOption = this.radioOptions.find((option: RadioOption): boolean => {\n\t\t\t\t\treturn option.value.toString() == selectedValue\n\t\t\t\t})\n\n\t\t\t\tif (selectedOption != null) {\n\t\t\t\t\tthis.selectOption(selectedOption.value)\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tgetCache(): void {\n\t\t\t\tif (this.isSave) {\n\t\t\t\t\tconst that = this\n\t\t\t\t\tuni.getStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tsuccess: (res: GetStorageSuccess) => {\n\t\t\t\t\t\t\tconst cacheData = res.data as string\n\t\t\t\t\t\t\tlet save_value: string | number\n\n\t\t\t\t\t\t\t// 根据varType转换类型\n\t\t\t\t\t\t\tif (that.varType == \"number\") {\n\t\t\t\t\t\t\t\tsave_value = parseFloat(cacheData)\n\t\t\t\t\t\t\t\t// 验证转换结果\n\t\t\t\t\t\t\t\tif (isNaN(save_value as number)) {\n\t\t\t\t\t\t\t\t\treturn // 转换失败，不使用缓存\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t// varType == \"string\"\n\t\t\t\t\t\t\t\tsave_value = cacheData\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tthat.fieldValue = save_value\n\t\t\t\t\t\t\tconst result: FormChangeEvent = {\n\t\t\t\t\t\t\t\tindex: this.index,\n\t\t\t\t\t\t\t\tvalue: save_value\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthis.change(result)\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tsetCache(): void {\n\t\t\t\tif (this.isSave && this.fieldValue != null) {\n\t\t\t\t\t// 统一以字符串形式存储\n\t\t\t\t\tconst cacheValue = this.fieldValue.toString()\n\t\t\t\t\tuni.setStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tdata: cacheValue\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tvalidate(): boolean {\n\t\t\t\t// 单选器验证\n\t\t\t\tif (this.fieldValue == null) {\n\t\t\t\t\tthis.showError = true\n\t\t\t\t\tthis.errorMessage = \"请选择一个选项\"\n\t\t\t\t\treturn false\n\t\t\t\t}\n\t\t\t\tthis.showError = false\n\t\t\t\tthis.errorMessage = \"\"\n\t\t\t\treturn true\n\t\t\t},\n\n\t\t\tchange(event: FormChangeEvent): void {\n\t\t\t\t// 更新字段值\n\t\t\t\tthis.fieldValue = event.value\n\t\t\t\t// 保存缓存\n\t\t\t\tthis.setCache()\n\t\t\t\t// 触发父组件事件\n\t\t\t\tthis.$emit('change', event)\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.radio-container {\n\t\tflex: 1;\n\t\tpadding: 10rpx 20rpx;\n\t}\n\n\t/* 竖向布局（默认） */\n\t.radio-container-vertical {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\n\t/* 横向布局 */\n\t.radio-container-horizontal {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tflex-wrap: wrap;\n\t}\n\n\t.radio-option {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t/* 横向布局时的选项样式 */\n\t.radio-container-horizontal .radio-option {\n\t\tmargin-right: 40rpx;\n\t\tmargin-bottom: 10rpx;\n\t}\n\n\t/* 横向布局时最后一个选项的样式 */\n\t.radio-container-horizontal .radio-option-last {\n\t\tmargin-right: 0;\n\t}\n\n\t.radio-option-last {\n\t\tmargin-bottom: 0;\n\t}\n\n\t.radio-item {\n\t\tmargin-right: 20rpx;\n\t}\n\n\t.radio-text {\n\t\tflex: 1;\n\t\tfont-size: 28rpx;\n\t\tcolor: #333333;\n\t\tline-height: 1.4;\n\t}\n\n\t/* 横向布局时文本不需要flex: 1 */\n\t.radio-container-horizontal .radio-text {\n\t\tflex: none;\n\t}\n</style>", null], "names": [], "mappings": ";;;;;;;;;;;;;+BA+GI;+BAkBM;AArGJ;;kBAyDJ,OAAW,IAAG,CAAA;YAEb,IAAM,WAAW,IAAI,CAAC,QAAM,CAAC,OAAM,CAAA,EAAA;YACnC,IAAI,CAAC,aAAa,CAAC;QACpB;;;;;UAdE,IAAQ,kBAAkB,EAAA;YAEzB,IAAM,WAAW,IAAI,KAAI;YACzB,IAAI,aAAa,IAAI,CAAC,UAAU,EAAE;gBACjC,IAAI,CAAC,UAAS,GAAI;;QAEpB;uBACA,OAAM,IAAG;;;;;;;;;eAjFZ,IAciB,2BAAA,IAdA,WAAO,KAAA,SAAS,EAAG,gBAAY,KAAA,SAAS,EAAG,SAAK,KAAA,GAAG,EAAG,mBAAe,KAAA,YAAY,EAAG,iBAAa,KAAA,UAAU,EAC1H,sBAAkB,KAAA,eAAe,OACvB,mBAAa,YACvB,gBASO,GAAA;mBAAA;gBATP,IASO,QAAA,IATA,WAAK,IAAA;oBAAA;oBAAsB,IAAA,KAAA,eAAe,KAAA;wBAAA;;wBAAA;;iBAAA;oBAChD,IAOc,wBAAA,IAPA,cAAQ,KAAA,aAAa,GAAA,6BAEjC,gBAAuC,GAAA;+BAAA;4BADxC,IAKO,UAAA,IAAA,EAAA,cAAA,UAAA,CAJoB,KAAA,YAAY,EAAA,IAA9B,QAAQ,OAAR,SAAM,UAAA,GAAA,CAAA;uCADf,IAKO,QAAA,IALA,WAAK,IAAE;oCAAA;oCAAA,IAAA,wBAAA,UAAA,KAAA,YAAA,CAAA,MAAA,GAAA,CAAA;iCAA4E,GAChD,SAAK;oCAC9C,IACgD,kBAAA,IADxC,WAAO,OAAO,KAAK,CAAC,QAAQ,IAAK,aAAS,KAAA,SAAS,CAAC,OAAO,KAAK,GACtE,WAAO,KAAA,UAAU,EAAE,WAAM;;;;;oCAC3B,IAAqF,QAAA,IAA/E,WAAM,cAAc,aAAK,KAAA;wCAAE,KAAA,YAAY,CAAC,OAAO,KAAK;oCAAA;2CAAM,OAAO,IAAI,GAAA,CAAA,EAAA;wCAAA;qCAAA;;;;;;;;;;;;;;;;;;;;;;;;;;aA+C7E;aACA;aACA;aACA;aACA;aACA;aACA,uBAAoB;aACpB;aACA;aACA;aACA;;;mBAVA,eAAW,IACX,gBAAY,IAAG,CAAA,EAAA,OACf,YAAQ,KAAK,EACb,cAAU,IACV,SAAK,IACL,aAAS,UACT,kBAAc,IAAM,gBACpB,gBAAY,WACZ,qBAAiB,YACjB,eAAW,KAAK,EAChB,kBAAc;;aAyBf;aAAA,qBAAc,uBAAuB,GAAG,IAAG,CAAA;QAC1C,IAAM,WAAW,SAAS,GAAE;QAC5B,IAAM,aAAa,SAAS,KAAI;QAGhC,IAAI,CAAC,SAAQ,GAAI,SAAS,IAAG;QAC7B,IAAI,CAAC,UAAS,GAAI;QAClB,IAAI,CAAC,MAAK,GAAI,SAAS,MAAK,IAAK,KAAI;QACrC,IAAI,CAAC,QAAO,GAAI,IAAI,CAAC,OAAM,GAAI,MAAM;QAGrC,IAAM,YAAY,SAAS,KAAI,CAAA,EAAA,CAAK;QACpC,IAAI,CAAC,GAAE,GAAI,UAAU,SAAS,CAAC,UAAU;QACzC,IAAM,gBAAgB,UAAU,SAAS,CAAC,cAAc;QAGxD,IAAI,CAAC,OAAM,GAAI,IAAI,CAAC,eAAe,CAAC;QACpC,IAAI,CAAC,UAAS,GAAI,UAAU,SAAS,CAAC,iBAAiB;QAGvD,IAAM,wBAAwB,UAAU,SAAS,CAAC,sBAAsB;QACxE,IAAI,CAAC,eAAc,GAAI,IAAI,CAAC,uBAAuB,CAAC;QAGpD,IAAM,eAAe,UAAU,QAAQ,CAAC;QACxC,IAAI,gBAAgB,IAAI,EAAE;YACzB,IAAI,CAAC,YAAW,GAAI,KAAC;gBACrB;gBAAK,IAAI,YAAI,CAAC;gBAAd,MAAgB,IAAI,aAAa,MAAM;oBACtC,IAAM,YAAY,YAAY,CAAC,EAAC,CAAA,EAAA,CAAK;oBACrC,IAAM,qBACL,OAAM,UAAU,SAAS,CAAC,WAAW,IACrC,QAAO,UAAU,GAAG,CAAC,YAAY;oBAElC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;oBANiB;;;;QAW1C,IAAI,CAAC,QAAQ;IACd;aAGA;aAAA,uBAAgB,SAAS,MAAM,GAAG,MAAK,CAAA;QACtC,IAAM,aAAa;YAAC;YAAU;SAAQ;QACtC,IAAI,WAAW,QAAQ,CAAC,UAAU;YACjC,OAAO;eACD;YACN,OAAO;;IAET;aAGA;aAAA,+BAAwB,iBAAiB,MAAM,GAAG,MAAK,CAAA;QACtD,IAAM,kBAAkB;YAAC;YAAY;SAAY;QACjD,IAAI,gBAAgB,QAAQ,CAAC,kBAAkB;YAC9C,OAAO;eACD;YACN,OAAO;;IAET;aAGA,UAAU,gBAA4B,GAAG,OAAM,CAAA;QAC9C,IAAI,IAAI,CAAC,UAAS,IAAK,IAAI,EAAE;YAC5B,OAAO,KAAI;;QAGZ,OAAO,eAAe,IAAI,CAAC,UAAS;IACrC;aAGA,aAAa,gBAA4B,GAAG,IAAG,CAAA;QAC9C,IAAI,qBAAiC;QAGrC,IAAI,IAAI,CAAC,OAAM,IAAK,UAAU;YAC7B,IAAI,oBAAO,mBAAkB,UAAU;gBACtC,gBAAgB,cAAY,EAAA,CAAA,MAAA;mBACtB;gBACN,gBAAgB,WAAW,CAAA,cAAa,EAAA,CAAA,MAAA,EAAC,QAAQ;gBACjD,IAAI,MAAM,cAAY,EAAA,CAAK,MAAM,GAAG;oBACnC,gBAAgB,CAAA;;aAElB;eACM;YAEN,gBAAgB,cAAc,QAAQ;;QAGvC,IAAM,yBACL,QAAO,IAAI,CAAC,KAAK,EACjB,QAAO;QAER,IAAI,CAAC,MAAM,CAAC;IACb;aAGA;aAAA,qBAAc,OAAO,wBAAwB,GAAG,IAAG,CAAA;QAClD,IAAM,gBAAgB,MAAM,MAAM,CAAC,KAAI;QAEvC,IAAM,iBAAiB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAC,QAAQ,cAAc,OAAM,CAAG;YAC7E,OAAO,OAAO,KAAK,CAAC,QAAQ,MAAM;QACnC;;QAEA,IAAI,kBAAkB,IAAI,EAAE;YAC3B,IAAI,CAAC,YAAY,CAAC,eAAe,KAAK;;IAExC;aAEA;aAAA,mBAAY,IAAG,CAAA;QACd,IAAI,IAAI,CAAC,MAAM,EAAE;YAChB,IAAM,OAAO,IAAG;YAChB,iCACC,MAAK,IAAI,CAAC,QAAQ,EAClB,UAAS,IAAC,KAAK,kBAAoB;gBAClC,IAAM,YAAY,IAAI,IAAG,CAAA,EAAA,CAAK,MAAK;gBACnC,IAAI;gBAGJ,IAAI,KAAK,OAAM,IAAK,UAAU;oBAC7B,aAAa,WAAW;oBAExB,IAAI,MAAM,WAAS,EAAA,CAAK,MAAM,GAAG;wBAChC;;uBAEK;oBAEN,aAAa;;gBAGd,KAAK,UAAS,GAAI;gBAClB,IAAM,yBACL,QAAO,IAAI,CAAC,KAAK,EACjB,QAAO;gBAER,IAAI,CAAC,MAAM,CAAC;YACb;;;IAGH;aAEA;aAAA,mBAAY,IAAG,CAAA;QACd,IAAI,IAAI,CAAC,MAAK,IAAK,IAAI,CAAC,UAAS,IAAK,IAAI,EAAE;YAE3C,IAAM,aAAa,IAAI,CAAC,UAAU,GAAC,QAAQ;YAC3C,iCACC,MAAK,IAAI,CAAC,QAAQ,EAClB,OAAM;;IAGT;aAEA;aAAA,mBAAY,OAAM,CAAA;QAEjB,IAAI,IAAI,CAAC,UAAS,IAAK,IAAI,EAAE;YAC5B,IAAI,CAAC,SAAQ,GAAI,IAAG;YACpB,IAAI,CAAC,YAAW,GAAI;YACpB,OAAO,KAAI;;QAEZ,IAAI,CAAC,SAAQ,GAAI,KAAI;QACrB,IAAI,CAAC,YAAW,GAAI;QACpB,OAAO,IAAG;IACX;aAEA;aAAA,cAAO,sBAAsB,GAAG,IAAG,CAAA;QAElC,IAAI,CAAC,UAAS,GAAI,MAAM,KAAI;QAE5B,IAAI,CAAC,QAAQ;QAEb,IAAI,CAAC,OAAK,CAAC,UAAU;IACtB;;mBA1OK;;;;;;;;;;;;;6FAWK,CAAA,qDAIA,0DAIA,mEAIA;;;;;;;;;AAqNZ"}