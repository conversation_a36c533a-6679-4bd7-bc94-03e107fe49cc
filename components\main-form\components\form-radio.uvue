<template>
	<form-container :label="fieldName" :show-error="showError" :tip="tip" :error-message="errorMessage" :label-color="labelColor"
		:background-color="backgroundColor">
		<template #input-content>
			<view class="radio-container">
				<radio-group @change="onRadioChange">
					<view :class="['radio-option', { 'radio-option-last': index === radioOptions.length - 1 }]"
						v-for="(option, index) in radioOptions" :key="index">
						<radio :value="option.value.toString()" :checked="isChecked(option.value)"
							:color="radioColor" class="radio-item"></radio>
						<text class="radio-text" @click="selectOption(option.value)">{{ option.text }}</text>
					</view>
				</radio-group>
			</view>
		</template>
	</form-container>
</template>

<script lang="uts">
	import { FormFieldData, FormChangeEvent } from '@/components/main-form/form_type.uts'
	import FormContainer from './form-container.uvue'

	// 定义选项类型
	type RadioOption = {
		text: string;
		value: string | number;
	}

	export default {
		name: "FormRadio",
		emits: ['change', 'msg'],
		components: {
			FormContainer
		},
		props: {
			data: {
				type: null as any as PropType<FormFieldData>
			},
			index: {
				type: Number,
				default: 0
			},
			keyName: {
				type: String,
				default: ""
			},
			labelColor: {
				type: String,
				default: "#000"
			},
			backgroundColor: {
				type: String,
				default: "#f1f4f9"
			}
		},
		data() {
			return {
				fieldName: "",
				fieldValue: null as string | number | null,
				isSave: false,
				save_key: "",
				tip: "",
				varType: "string",
				radioOptions: [] as RadioOption[],
				radioColor: "#007aff",
				showError: false,
				errorMessage: ""
			}
		},
		computed: {

		},
		watch: {
			data: {
				handler(obj: FormFieldData) {
					// 只处理value的变化，当外部传入的value与当前fieldValue不同时，才更新fieldValue
					const newValue = obj.value
					if (newValue !== this.fieldValue) {
						this.fieldValue = newValue
					}
				},
				deep: true
			}
		},
		created(): void {
			// 初始化时调用一次即可
			const fieldObj = this.$props["data"] as FormFieldData
			this.initFieldData(fieldObj)
		},
		methods: {
			// 初始化字段数据（仅在首次加载时调用）
			initFieldData(fieldObj: FormFieldData): void {
				const fieldKey = fieldObj.key
				const fieldValue = fieldObj.value

				// 设置基本信息
				this.fieldName = fieldObj.name
				this.fieldValue = fieldValue
				this.isSave = fieldObj.isSave ?? false
				this.save_key = this.keyName + "_" + fieldKey

				// 解析配置信息
				const extalJson = fieldObj.extra as UTSJSONObject
				this.tip = extalJson.getString("tip") ?? ""
				const configVarType = extalJson.getString("varType") ?? "string"

				// 校验 varType 的有效性
				this.varType = this.validateVarType(configVarType)
				this.radioColor = extalJson.getString("radioColor") ?? "#007aff"

				// 解析选项数据
				const optionsArray = extalJson.getArray("options")
				if (optionsArray != null) {
					this.radioOptions = []
					for (let i = 0; i < optionsArray.length; i++) {
						const optionObj = optionsArray[i] as UTSJSONObject
						const option: RadioOption = {
							text: optionObj.getString("text") ?? "",
							value: optionObj.get("value") ?? ""
						}
						this.radioOptions.push(option)
					}
				}

				// 获取缓存
				this.getCache()
			},

			// 校验 varType 的有效性
			validateVarType(varType: string): string {
				const validTypes = ["string", "number"]
				if (validTypes.includes(varType)) {
					return varType
				} else {
					return "string"
				}
			},

			// 判断选项是否被选中
			isChecked(optionValue: string | number): boolean {
				if (this.fieldValue == null) {
					return false
				}
				// 使用宽松比较来处理类型差异
				return optionValue == this.fieldValue
			},

			// 选择选项
			selectOption(optionValue: string | number): void {
				let selectedValue: string | number = optionValue

				// 根据varType转换类型
				if (this.varType == "number") {
					if (typeof selectedValue === "number") {
						selectedValue = selectedValue
					} else {
						selectedValue = parseFloat(selectedValue.toString())
						if (isNaN(selectedValue as number)) {
							selectedValue = 0
						}
					}
				} else {
					// varType == "string"
					selectedValue = selectedValue.toString()
				}

				const result: FormChangeEvent = {
					index: this.index,
					value: selectedValue
				}
				this.change(result)
			},

			// radio-group change事件处理
			onRadioChange(event: UniRadioGroupChangeEvent): void {
				const selectedValue = event.detail.value
				// 查找对应的选项
				const selectedOption = this.radioOptions.find((option: RadioOption): boolean => {
					return option.value.toString() == selectedValue
				})

				if (selectedOption != null) {
					this.selectOption(selectedOption.value)
				}
			},

			getCache(): void {
				if (this.isSave) {
					const that = this
					uni.getStorage({
						key: this.save_key,
						success: (res: GetStorageSuccess) => {
							const cacheData = res.data as string
							let save_value: string | number

							// 根据varType转换类型
							if (that.varType == "number") {
								save_value = parseFloat(cacheData)
								// 验证转换结果
								if (isNaN(save_value as number)) {
									return // 转换失败，不使用缓存
								}
							} else {
								// varType == "string"
								save_value = cacheData
							}

							that.fieldValue = save_value
							const result: FormChangeEvent = {
								index: this.index,
								value: save_value
							}
							this.change(result)
						}
					})
				}
			},

			setCache(): void {
				if (this.isSave && this.fieldValue != null) {
					// 统一以字符串形式存储
					const cacheValue = this.fieldValue.toString()
					uni.setStorage({
						key: this.save_key,
						data: cacheValue
					})
				}
			},

			validate(): boolean {
				// 单选器验证
				if (this.fieldValue == null) {
					this.showError = true
					this.errorMessage = "请选择一个选项"
					return false
				}
				this.showError = false
				this.errorMessage = ""
				return true
			},

			change(event: FormChangeEvent): void {
				// 更新字段值
				this.fieldValue = event.value
				// 保存缓存
				this.setCache()
				// 触发父组件事件
				this.$emit('change', event)
			}
		}
	}
</script>

<style>
	.radio-container {
		flex: 1;
		padding: 10rpx 20rpx;
	}

	.radio-option {
		display: flex;
		flex-direction: row;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.radio-option-last {
		margin-bottom: 0;
	}

	.radio-item {
		margin-right: 20rpx;
	}

	.radio-text {
		flex: 1;
		font-size: 28rpx;
		color: #333333;
		line-height: 1.4;
	}
</style>